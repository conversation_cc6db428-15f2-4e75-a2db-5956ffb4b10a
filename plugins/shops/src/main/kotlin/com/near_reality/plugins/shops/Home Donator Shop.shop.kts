package com.near_reality.plugins.shops

import com.zenyte.game.model.shop.ShopCurrency

"Donator store"(1805, ShopCurrency.DONOR_POINTS, NO_SELLING) {
    32215(10, 85, 2400) //mbox bundle
    32209(10, 85, 650) //3rd age box
    32357(10, 85, 195) //easter box
    30031(10, 85, 195) //pet box
    32163(10, 85, 235) //cosmetic box
    32206(10, 85, 450) //ultimate box e
    32203(10, 85, 150) //pvp box
    32231(10, 85, 260) //regal box
    32164(10, 85, 180) //super m box
    32165(10, 85, 325) // ultimate m box
    32212(10, 85, 65) // skilling box
    6199(10, 85, 130) // mbox
    32149(10, 10, 100) //Larrrans key booster
    32201(10, 85, 130) //drop rate scroll
    32150(10, 85, 100) //gano boost
    32151(10, 85, 50) //slayer boost
    32152(10, 85, 100) //pet booster
    32153(10, 85, 100) //gauntlet booster
    32154(10, 85, 100) //blood money booster
    32155(10, 85, 100) //clue booster
    32156(10, 85, 100) //tob booster
    32166(10, 85, 100) //rev booster
    32167(10, 85, 100) //nex booster
    28526(10, 85, 390) //Sigil ninja
    26141(10, 85, 260) //Sigil remote storage
    11863(10, 85, 2600) // rainbow partyhat
    32224(10, 85, 1950) //partyhat
    32066(10, 85, 1850) //partyhat
    33068(10, 85, 1750) //partyhat
    27828(10, 85, 1500) //partyhat
    33001(10 as T, 85, 1300) //Korasi
    11730(100, 85, 2) // Overload
}